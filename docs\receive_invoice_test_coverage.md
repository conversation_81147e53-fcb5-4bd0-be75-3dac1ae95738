# 进项发票管理接口测试覆盖率检查

## 接口信息汇总

| 接口名称 | 接口路径 | 请求方式 | 所属模块 |
|----------|----------|----------|----------|
| 乐企发票获取 | /fpyp/fpmxPgae | POST | gtc-service |
| 分页查询进项发票 | /base-billing-manager/v1/statement/ReceiveInvoice/pagequery | POST | base-billing-manager |
| 给账单绑定进项发票 | /base-billing-manager/v1/statement/ReceiveInvoice/bindStatement | POST | base-billing-manager |
| 刷新进项发票列表 | /base-billing-manager/v1/statement/ReceiveInvoice/refresh | POST | base-billing-manager |

## 测试覆盖率检查

| 检查项 | 是否覆盖 | 说明/备注 |
|----------------|----------|---------------------|
| 正常流程 | 是 | 覆盖所有4个接口的正常业务流程 |
| 必填参数缺失 | 是 | 测试companyCode、statementNo、cooperatorId等必填参数缺失场景 |
| 参数类型错误 | 是 | 测试页码为负数、日期格式错误、参数为null等场景 |
| 参数越界 | 是 | 测试pageSize超限、页码为0等越界场景 |
| 无权限/未登录 | 是 | 测试token无效、过期、格式错误、无权限等场景 |
| 串场景 | 是 | 完整业务流程：乐企获取→刷新→查询→绑定 |
| 关键字段校验 | 是 | 验证发票号、金额、销售方名称等关键字段 |
| 响应结构校验 | 是 | 使用type_match验证响应数据结构 |
| 业务规则校验 | 是 | 验证BIND来源发票拦截、重复绑定等业务规则 |

## 测试场景详细说明

### 1. 正常流程测试
- **乐企发票获取**：测试正常参数获取发票列表
- **分页查询进项发票**：测试正常分页查询功能
- **账单绑定进项发票**：测试正常绑定操作
- **刷新进项发票列表**：测试正常刷新功能

### 2. 异常场景测试
- **参数验证**：空参数、null参数、格式错误参数
- **业务规则**：BIND来源拦截、重复绑定、发票不存在
- **权限控制**：未授权、token过期、无权限
- **系统异常**：服务不可用、数据库超时

### 3. 业务流程测试
- **完整流程**：从乐企获取到最终绑定的完整业务链路
- **数据一致性**：验证乐企数据与系统数据的一致性
- **并发操作**：测试同时刷新和查询的并发场景
- **大数据量**：测试大量发票数据的处理能力

### 4. 数据验证重点
- **发票基础信息**：发票号码、开票日期、金额等
- **企业信息**：销售方、购买方纳税人识别号和名称
- **文件链接**：OFD、PDF、XML文件地址有效性
- **分页信息**：总数、页码、页大小等分页参数

## 测试文件结构

```
├── api/
│   ├── gtc-service/
│   │   └── fpyp/
│   │       └── fpmxPgae.yml
│   └── base-billing-manager/
│       └── statement/
│           └── ReceiveInvoice/
│               ├── pagequery.yml
│               ├── bindStatement.yml
│               └── refresh.yml
├── testcases/
│   └── billing/
│       ├── receive_invoice_single_test.yml
│       └── receive_invoice_business_flow_test.yml
├── exceptionCases/
│   ├── receive_invoice_exception_test.yml
│   └── receive_invoice_exception_suite.yml
├── testsuites/
│   └── receive_invoice_test_suite.yml
└── docs/
    └── receive_invoice_test_coverage.md
```

## 执行建议

1. **优先执行正常流程测试**：确保基本功能正常
2. **执行业务流程测试**：验证端到端业务场景
3. **执行异常用例测试**：验证系统健壮性
4. **关注数据一致性**：重点验证乐企与系统数据同步

## 环境变量配置

测试执行前需要配置以下环境变量：
- `base_url`: 基础服务地址
- `gtc_base_url`: GTC服务地址  
- `auth_token`: 认证token
- 测试数据相关变量（账单号、合作方ID等）
