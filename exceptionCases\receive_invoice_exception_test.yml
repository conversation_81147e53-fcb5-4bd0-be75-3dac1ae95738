- config:
    name: 进项发票管理异常用例测试
    base_url: ${ENV(base_url)}
    variables:
        - token: ${ENV(auth_token)}
        - gtc_base_url: ${ENV(gtc_base_url)}

# 1. 乐企发票获取接口异常测试
- test:
    name: 乐企发票获取-请求体为空
    variables:
        - json: {}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "请求参数"]

- test:
    name: 乐企发票获取-公司编码为null
    variables:
        - json: {"companyCode": null, "query": {"dataType": "1", "page": 1, "limit": 10}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "公司编码"]

- test:
    name: 乐企发票获取-日期格式错误
    variables:
        - json: {"companyCode": "esign", "query": {"dataType": "1", "page": 1, "limit": 10, "invoiceStartDate": "invalid-date", "invoiceEndDate": "2025-01-31"}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "日期格式"]

- test:
    name: 乐企发票获取-页码为负数
    variables:
        - json: {"companyCode": "esign", "query": {"dataType": "1", "page": -1, "limit": 10}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "页码"]

- test:
    name: 乐企发票获取-每页数量超限
    variables:
        - json: {"companyCode": "esign", "query": {"dataType": "1", "page": 1, "limit": 1000}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "每页数量"]

# 2. 分页查询进项发票异常测试
- test:
    name: 分页查询进项发票-请求体为空
    variables:
        - json: {}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]

- test:
    name: 分页查询进项发票-账单号格式错误
    variables:
        - json: {"statementNo": "INVALID_FORMAT_123", "currentPage": 1, "pageSize": 10}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "账单号格式"]

- test:
    name: 分页查询进项发票-页码为0
    variables:
        - json: {"statementNo": "ST202501010001", "currentPage": 0, "pageSize": 10}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]

- test:
    name: 分页查询进项发票-每页大小为0
    variables:
        - json: {"statementNo": "ST202501010001", "currentPage": 1, "pageSize": 0}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]

- test:
    name: 分页查询进项发票-每页大小超限
    variables:
        - json: {"statementNo": "ST202501010001", "currentPage": 1, "pageSize": 1000}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "每页大小"]

# 3. 账单绑定进项发票异常测试
- test:
    name: 账单绑定进项发票-请求体为空
    variables:
        - json: {}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]

- test:
    name: 账单绑定进项发票-账单号为null
    variables:
        - json: {"statementNo": null, "invoiceNos": ["25000000000000045037"]}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "账单号"]

- test:
    name: 账单绑定进项发票-发票号列表为null
    variables:
        - json: {"statementNo": "ST202501010001", "invoiceNos": null}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "发票号"]

- test:
    name: 账单绑定进项发票-发票号包含空字符串
    variables:
        - json: {"statementNo": "ST202501010001", "invoiceNos": ["25000000000000045037", "", "25000000000000045038"]}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "发票号不能为空"]

- test:
    name: 账单绑定进项发票-发票号重复
    variables:
        - json: {"statementNo": "ST202501010001", "invoiceNos": ["25000000000000045037", "25000000000000045037"]}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "发票号重复"]

- test:
    name: 账单绑定进项发票-发票来源为BIND的拦截测试
    variables:
        - json: {"statementNo": "ST202501010001", "invoiceNos": ["BIND_SOURCE_INVOICE_001"]}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "发票来源为BIND，不允许重复绑定"]

# 4. 刷新进项发票列表异常测试
- test:
    name: 刷新进项发票列表-请求体为空
    variables:
        - json: {}
    api: api/base-billing-manager/statement/ReceiveInvoice/refresh.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]

- test:
    name: 刷新进项发票列表-合作方ID为null
    variables:
        - json: {"cooperatorId": null, "cooperatorMode": "CHANNEL", "companyCode": "esign"}
    api: api/base-billing-manager/statement/ReceiveInvoice/refresh.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "合作方ID"]

- test:
    name: 刷新进项发票列表-合作方模式无效
    variables:
        - json: {"cooperatorId": "COOP001", "cooperatorMode": "INVALID_MODE", "companyCode": "esign"}
    api: api/base-billing-manager/statement/ReceiveInvoice/refresh.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "合作方模式"]

- test:
    name: 刷新进项发票列表-公司编码为空字符串
    variables:
        - json: {"cooperatorId": "COOP001", "cooperatorMode": "CHANNEL", "companyCode": ""}
    api: api/base-billing-manager/statement/ReceiveInvoice/refresh.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "公司编码"]

# 5. 权限相关异常测试
- test:
    name: Token过期-分页查询进项发票
    variables:
        - json: {"statementNo": "ST202501010001", "currentPage": 1, "pageSize": 10}
        - token: "expired_token_12345"
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 401]
        - eq: ["content.success", false]
        - contains: ["content.message", "Token过期"]

- test:
    name: Token格式错误-账单绑定进项发票
    variables:
        - json: {"statementNo": "ST202501010001", "invoiceNos": ["25000000000000045037"]}
        - token: "invalid_format_token"
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 401]
        - eq: ["content.success", false]
        - contains: ["content.message", "Token格式错误"]

- test:
    name: 无权限访问-刷新进项发票列表
    variables:
        - json: {"cooperatorId": "COOP001", "cooperatorMode": "CHANNEL", "companyCode": "esign"}
        - token: "no_permission_token"
    api: api/base-billing-manager/statement/ReceiveInvoice/refresh.yml
    validate:
        - eq: ["status_code", 403]
        - eq: ["content.success", false]
        - contains: ["content.message", "无权限"]

# 6. 系统异常测试
- test:
    name: 系统异常-乐企服务不可用
    variables:
        - json: {"companyCode": "esign", "query": {"dataType": "1", "page": 1, "limit": 10}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    validate:
        - in: ["status_code", [500, 502, 503, 504]]
        - eq: ["content.success", false]
        - contains: ["content.message", "服务不可用"]

- test:
    name: 系统异常-数据库连接超时
    variables:
        - json: {"statementNo": "ST202501010001", "currentPage": 1, "pageSize": 10}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - in: ["status_code", [500, 504]]
        - eq: ["content.success", false]
        - contains: ["content.message", "数据库"]
