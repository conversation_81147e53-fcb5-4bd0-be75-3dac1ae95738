- config:
    name: 进项发票管理业务流程测试
    base_url: ${ENV(base_url)}
    variables:
        - token: ${ENV(auth_token)}
        - gtc_base_url: ${ENV(gtc_base_url)}
        - test_company_code: "esign"
        - test_cooperator_id: "COOP001"
        - test_statement_no: "ST202501010001"

# 完整业务流程：乐企获取发票 -> 刷新发票列表 -> 分页查询 -> 绑定账单
- test:
    name: 步骤1-从乐企获取最新发票数据
    variables:
        - json: {"companyCode": "${test_company_code}", "query": {"dataType": "1", "page": 1, "limit": 5, "invoiceStartDate": "2025-01-01", "invoiceEndDate": "2025-01-31"}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    extract:
        - leqi_invoice_list: content.data.list
        - leqi_total_count: content.data.totalRow
        - first_invoice_no: content.data.list.0.fphm
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]
        - type_match: ["$leqi_invoice_list", "list"]
        - gte: ["$leqi_total_count", 0]

- test:
    name: 步骤2-刷新系统进项发票列表
    variables:
        - json: {"cooperatorId": "${test_cooperator_id}", "cooperatorMode": "CHANNEL", "companyCode": "${test_company_code}"}
    api: api/base-billing-manager/statement/ReceiveInvoice/refresh.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - eq: ["content.data", true]

- test:
    name: 步骤3-分页查询系统进项发票
    variables:
        - json: {"statementNo": "${test_statement_no}", "currentPage": 1, "pageSize": 10}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    extract:
        - system_invoice_list: content.data.list
        - system_total_count: content.data.totalCount
        - available_invoice_nos: content.data.list.*.invoiceNo
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - type_match: ["$system_invoice_list", "list"]
        - gte: ["$system_total_count", 0]

- test:
    name: 步骤4-将进项发票绑定到账单
    variables:
        - json: {"statementNo": "${test_statement_no}", "invoiceNos": ["${first_invoice_no}"]}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - eq: ["content.data", true]

- test:
    name: 步骤5-验证绑定结果-再次查询发票列表
    variables:
        - json: {"statementNo": "${test_statement_no}", "currentPage": 1, "pageSize": 10}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - type_match: ["content.data.list", "list"]
        - contains: ["content.data.list.*.statementNo", "${test_statement_no}"]

# 异常业务流程测试
- test:
    name: 异常流程1-绑定不存在的发票号
    variables:
        - json: {"statementNo": "${test_statement_no}", "invoiceNos": ["INVALID_INVOICE_NO_999999"]}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 404]
        - eq: ["content.success", false]
        - contains: ["content.message", "发票不存在"]

- test:
    name: 异常流程2-重复绑定同一发票
    variables:
        - json: {"statementNo": "${test_statement_no}", "invoiceNos": ["${first_invoice_no}"]}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "发票已绑定"]

# 数据一致性验证流程
- test:
    name: 数据一致性验证-乐企数据与系统数据对比
    variables:
        - json: {"companyCode": "${test_company_code}", "query": {"dataType": "1", "page": 1, "limit": 10, "invoiceNo": "${first_invoice_no}"}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    extract:
        - leqi_invoice_detail: content.data.list.0
        - leqi_invoice_amount: content.data.list.0.jshj
        - leqi_saler_name: content.data.list.0.xsfmc
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - type_match: ["$leqi_invoice_detail", "dict"]
        - type_match: ["$leqi_invoice_amount", "number"]

- test:
    name: 数据一致性验证-系统中对应发票数据验证
    variables:
        - json: {"statementNo": "", "currentPage": 1, "pageSize": 10}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - contains: ["content.data.list.*.invoiceNo", "${first_invoice_no}"]
        - eq: ["content.data.list.0.includeTaxAmount", "$leqi_invoice_amount"]
        - eq: ["content.data.list.0.salerName", "$leqi_saler_name"]

# 并发操作测试流程
- test:
    name: 并发测试-同时刷新和查询发票列表
    variables:
        - json: {"cooperatorId": "${test_cooperator_id}", "cooperatorMode": "CHANNEL", "companyCode": "${test_company_code}"}
    api: api/base-billing-manager/statement/ReceiveInvoice/refresh.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]

- test:
    name: 并发测试-刷新期间查询发票列表
    variables:
        - json: {"statementNo": "${test_statement_no}", "currentPage": 1, "pageSize": 10}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - type_match: ["content.data", "dict"]

# 大数据量测试流程
- test:
    name: 大数据量测试-获取大量发票数据
    variables:
        - json: {"companyCode": "${test_company_code}", "query": {"dataType": "1", "page": 1, "limit": 100, "invoiceStartDate": "2024-01-01", "invoiceEndDate": "2025-01-31"}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    extract:
        - large_invoice_list: content.data.list
        - large_total_count: content.data.totalRow
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - lte: ["content.data.pageSize", 100]
        - gte: ["$large_total_count", 0]

- test:
    name: 大数据量测试-批量绑定多个发票
    variables:
        - json: {"statementNo": "${test_statement_no}", "invoiceNos": ["${large_invoice_list.0.fphm}", "${large_invoice_list.1.fphm}", "${large_invoice_list.2.fphm}"]}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - eq: ["content.data", true]
