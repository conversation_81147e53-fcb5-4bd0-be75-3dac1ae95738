- config:
    name: 进项发票管理单接口测试
    base_url: ${ENV(base_url)}
    variables:
        - token: ${ENV(auth_token)}
        - gtc_base_url: ${ENV(gtc_base_url)}

# 1. 乐企发票获取接口测试
- test:
    name: 乐企发票获取-正常流程
    variables:
        - json: {"companyCode": "esign", "query": {"dataType": "1", "page": 1, "limit": 10, "invoiceStartDate": "2025-01-01", "invoiceEndDate": "2025-01-31"}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]
        - type_match: ["content.data", "dict"]
        - type_match: ["content.data.list", "list"]
        - gte: ["content.data.totalRow", 0]
        - gte: ["content.data.pageSize", 1]
        - gte: ["content.data.pageNumber", 1]

- test:
    name: 乐企发票获取-参数缺失测试
    variables:
        - json: {"query": {"dataType": "1", "page": 1, "limit": 10}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "companyCode"]

- test:
    name: 乐企发票获取-页码参数错误测试
    variables:
        - json: {"companyCode": "esign", "query": {"dataType": "1", "page": 0, "limit": 10}}
    api: api/gtc-service/fpyp/fpmxPgae.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "页码"]

# 2. 分页查询进项发票接口测试
- test:
    name: 分页查询进项发票-正常流程
    variables:
        - json: {"statementNo": "ST202501010001", "currentPage": 1, "pageSize": 10}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    extract:
        - invoice_list: content.data.list
        - total_count: content.data.totalCount
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - type_match: ["content.data", "dict"]
        - type_match: ["$invoice_list", "list"]
        - gte: ["$total_count", 0]

- test:
    name: 分页查询进项发票-空账单号测试
    variables:
        - json: {"statementNo": "", "currentPage": 1, "pageSize": 10}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "账单号"]

- test:
    name: 分页查询进项发票-页码超限测试
    variables:
        - json: {"statementNo": "ST202501010001", "currentPage": -1, "pageSize": 10}
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]

# 3. 给账单绑定进项发票接口测试
- test:
    name: 账单绑定进项发票-正常流程
    variables:
        - json: {"statementNo": "ST202501010001", "invoiceNos": ["25000000000000045037", "25000000000000045038"]}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - eq: ["content.data", true]

- test:
    name: 账单绑定进项发票-发票号列表为空测试
    variables:
        - json: {"statementNo": "ST202501010001", "invoiceNos": []}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "发票号"]

- test:
    name: 账单绑定进项发票-账单号不存在测试
    variables:
        - json: {"statementNo": "INVALID_STATEMENT", "invoiceNos": ["25000000000000045037"]}
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 404]
        - eq: ["content.success", false]
        - contains: ["content.message", "账单不存在"]

# 4. 刷新进项发票列表接口测试
- test:
    name: 刷新进项发票列表-正常流程
    variables:
        - json: {"cooperatorId": "COOP001", "cooperatorMode": "CHANNEL", "companyCode": "esign"}
    api: api/base-billing-manager/statement/ReceiveInvoice/refresh.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.success", true]
        - eq: ["content.data", true]

- test:
    name: 刷新进项发票列表-合作方ID缺失测试
    variables:
        - json: {"cooperatorMode": "CHANNEL", "companyCode": "esign"}
    api: api/base-billing-manager/statement/ReceiveInvoice/refresh.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "合作方ID"]

- test:
    name: 刷新进项发票列表-公司编码无效测试
    variables:
        - json: {"cooperatorId": "COOP001", "cooperatorMode": "CHANNEL", "companyCode": "INVALID_CODE"}
    api: api/base-billing-manager/statement/ReceiveInvoice/refresh.yml
    validate:
        - eq: ["status_code", 400]
        - eq: ["content.success", false]
        - contains: ["content.message", "公司编码"]

# 权限验证测试
- test:
    name: 未授权访问-分页查询进项发票
    variables:
        - json: {"statementNo": "ST202501010001", "currentPage": 1, "pageSize": 10}
        - token: "invalid_token"
    api: api/base-billing-manager/statement/ReceiveInvoice/pagequery.yml
    validate:
        - eq: ["status_code", 401]
        - eq: ["content.success", false]
        - contains: ["content.message", "未授权"]

- test:
    name: 未授权访问-账单绑定进项发票
    variables:
        - json: {"statementNo": "ST202501010001", "invoiceNos": ["25000000000000045037"]}
        - token: "invalid_token"
    api: api/base-billing-manager/statement/ReceiveInvoice/bindStatement.yml
    validate:
        - eq: ["status_code", 401]
        - eq: ["content.success", false]
        - contains: ["content.message", "未授权"]
