# 【计费】赠送管理支持通过补充协议并关联合同后发起并调整赠送订单生效流程-测试用例

## 功能测试

### 创建特殊审批赠送时支持关联合同

#### TL-创建特殊审批赠送关联合同成功验证

##### PD-前置条件：具有特殊审批赠送创建权限；存在有效合同；客户信息已录入；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送创建页面

##### 步骤二：在基本信息中录入客户GID信息

##### 步骤三：在关联合同处录入有效合同编号，系统自动校验合同合法性

##### 步骤四：勾选"合同上线后自动生效"配置项

##### 步骤五：完成其他必填信息填写并提交

##### ER-预期结果：1：合同关联成功；2：系统显示合同编号；3：配置项生效；4：特殊审批赠送创建成功；5：列表中显示关联合同信息；

#### TL-创建特殊审批赠送关联合同校验客户信息未录入

##### PD-前置条件：具有特殊审批赠送创建权限；存在有效合同；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送创建页面

##### 步骤二：未录入客户信息，直接在关联合同处录入合同编号

##### ER-预期结果：1：系统toast提示"请先填写客户信息"；2：合同关联失败；

#### TL-创建特殊审批赠送关联合同校验客户非合同主体

##### PD-前置条件：具有特殊审批赠送创建权限；存在有效合同；客户不是合同签署/被转售/最终使用主体；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送创建页面

##### 步骤二：录入非合同主体的客户GID信息

##### 步骤三：在关联合同处录入合同编号

##### ER-预期结果：1：系统toast提示"该客户必须是合同的签署/被转售/最终使用主体，请重新录入"；2：合同关联失败；

#### TL-创建特殊审批赠送关联合同校验合同阶段非预合同已确认

##### PD-前置条件：具有特殊审批赠送创建权限；合同阶段不是预合同/已确认状态；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送创建页面

##### 步骤二：录入客户GID信息

##### 步骤三：在关联合同处录入非预合同/已确认阶段的合同编号

##### ER-预期结果：1：系统toast提示"合同阶段必须是预合同/已确认，请重新录入"；2：合同关联失败；

### 创建特殊审批赠送时支持同步创建赠送告知函

#### TL-营销赠送战略赠送勾选同步创建赠送告知函成功

##### PD-前置条件：具有特殊审批赠送创建权限；选择营销赠送或战略赠送场景；存在有效合同；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送创建页面

##### 步骤二：选择赠送场景为营销赠送或战略赠送

##### 步骤三：勾选"同步创建赠送告知函"选项

##### 步骤四：录入客户信息和关联合同编号

##### 步骤五：完成商品选择和其他信息填写，提交审批

##### 步骤六：审批通过后查看赠送告知函生成情况

##### ER-预期结果：1：同步创建赠送告知函选项显示；2：关联合同变为必填项；3：审批通过后自动创建赠送告知函；4：告知函包含客户名称、合同编码、产品信息、有效期等内容；5：支持下载赠送告知函；

#### TL-非营销战略赠送场景不显示同步创建赠送告知函选项

##### PD-前置条件：具有特殊审批赠送创建权限；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送创建页面

##### 步骤二：选择赠送场景为纯试用送等非营销赠送/战略赠送场景

##### ER-预期结果：1：页面不显示"同步创建赠送告知函"选项；2：关联合同保持非必填状态；

#### TL-勾选同步创建赠送告知函未关联合同提交校验

##### PD-前置条件：具有特殊审批赠送创建权限；选择营销赠送场景；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送创建页面

##### 步骤二：选择赠送场景为营销赠送

##### 步骤三：勾选"同步创建赠送告知函"选项

##### 步骤四：录入客户信息但不关联合同

##### 步骤五：完成其他信息填写并提交

##### ER-预期结果：1：系统校验失败；2：提示关联合同为必填项；3：特殊审批赠送提交失败；

### 优化特殊审批赠送时有效期信息录入方式

#### TL-添加商品时有效期配置为生效日期加生效时长

##### PD-前置条件：具有特殊审批赠送创建权限；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送创建页面

##### 步骤二：点击添加商品，进入商品选择弹窗

##### 步骤三：选择第一种有效期配置方式

##### 步骤四：设置生效日期为未来某个日期

##### 步骤五：设置生效时长为180天

##### 步骤六：保存商品配置

##### ER-预期结果：1：有效期配置显示为生效日期+生效时长两个字段；2：生效日期必填且可修改；3：生效时长必填且限制1-366整数；4：商品配置保存成功；

#### TL-生效日期早于系统日期提交校验

##### PD-前置条件：具有特殊审批赠送创建权限；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送创建页面

##### 步骤二：添加商品并设置生效日期为昨天

##### 步骤三：完成其他信息填写并提交

##### ER-预期结果：1：系统校验失败；2：提示生效日期不可早于系统日期；3：特殊审批赠送提交失败；

### 特殊审批赠送审批通过后新增待生效已作废状态和相关操作

#### TL-创建赠送告知函的特殊审批赠送审批通过后进入待生效状态

##### PD-前置条件：具有特殊审批赠送创建权限；已创建包含赠送告知函的特殊审批赠送；

##### 步骤一：提交特殊审批赠送进入审批流程

##### 步骤二：审批人员审批通过

##### 步骤三：查看特殊审批赠送状态变化

##### ER-预期结果：1：审批通过后状态变为"待生效"；2：不自动生成赠送订单；3：用户端费用中心不展示该订单；4：支持生效、作废操作；

#### TL-待生效状态手动生效成功验证

##### PD-前置条件：存在待生效状态的特殊审批赠送；关联合同已上线；生效日期不早于当日；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送列表

##### 步骤二：找到待生效状态的赠送记录

##### 步骤三：点击"生效"操作按钮

##### 步骤四：确认生效操作

##### ER-预期结果：1：生效操作成功；2：状态变更为"已完成"；3：生成赠送订单；4：用户端费用中心可查看订单；

#### TL-待生效状态生效时关联合同未上线校验

##### PD-前置条件：存在待生效状态的特殊审批赠送；关联合同未上线；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送列表

##### 步骤二：找到待生效状态的赠送记录

##### 步骤三：点击"生效"操作按钮

##### ER-预期结果：1：系统toast提示"关联合同订单上线前，暂无法生效"；2：生效操作失败；3：状态保持待生效；

#### TL-待生效状态生效时生效日期早于当日处理

##### PD-前置条件：存在待生效状态的特殊审批赠送；生效日期早于当日；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送列表

##### 步骤二：找到待生效状态的赠送记录

##### 步骤三：点击"生效"操作按钮

##### ER-预期结果：1：系统提示"销售生效开始日期将更新为今天，生效时长不变，是否继续"；2：选择继续后生效成功；3：生效日期更新为当日；4：生效时长保持不变；

#### TL-待生效状态手动作废成功验证

##### PD-前置条件：存在待生效状态的特殊审批赠送；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送列表

##### 步骤二：找到待生效状态的赠送记录

##### 步骤三：点击"作废"操作按钮

##### 步骤四：确认作废操作

##### ER-预期结果：1：弹出二次确认提示；2：确认后作废成功；3：状态变更为"已作废"；4：不可撤回操作；

#### TL-关联合同作废时特殊审批赠送同步作废

##### PD-前置条件：存在关联合同的待生效特殊审批赠送；合同未上线；

##### 步骤一：在合同管理系统中找到关联的合同

##### 步骤二：执行合同作废操作

##### 步骤三：查看特殊审批赠送状态变化

##### ER-预期结果：1：合同作废成功；2：关联的特殊审批赠送状态同步变更为"已作废"；3：作废操作不可撤回；

## 接口测试

### 关联合同确认预校验接口测试

#### TL-关联合同预校验接口正常调用验证

##### PD-前置条件：接口服务正常；存在有效合同和客户数据；

##### 步骤一：构造正确的请求参数，包含approvalType、contractId、gid

##### 步骤二：调用/base-billing-manager/approval/precheck/contractBind接口

##### 步骤三：验证返回结果

##### ER-预期结果：1：接口调用成功；2：返回code为0；3：data字段返回true表示校验通过；

#### TL-关联合同预校验接口参数校验

##### PD-前置条件：接口服务正常；

##### 步骤一：构造缺少必填参数的请求

##### 步骤二：调用预校验接口

##### 步骤三：验证错误返回

##### ER-预期结果：1：接口返回参数校验失败；2：提示缺少必填参数；3：data字段返回false；

#### TL-关联合同预校验接口业务校验失败

##### PD-前置条件：接口服务正常；客户不是合同主体；

##### 步骤一：构造客户非合同主体的请求参数

##### 步骤二：调用预校验接口

##### 步骤三：验证业务校验结果

##### ER-预期结果：1：接口调用成功；2：返回code为0；3：data字段返回false表示校验不通过；

### 发起赠送审批接口测试

#### TL-发起赠送审批接口新增配置规则验证

##### PD-前置条件：接口服务正常；配置了COMMODITY_SCOPE_LIMIT和ENABLE_NOTICE_LETTER规则；

##### 步骤一：构造营销赠送类型的请求参数

##### 步骤二：调用发起赠送审批接口

##### 步骤三：验证配置规则生效情况

##### ER-预期结果：1：接口调用成功；2：营销赠送支持创建告知函；3：限制商品选择规则生效；4：配套服务产品线商品不可选择；

## 安全测试

### 身份认证与授权测试

#### TL-无权限用户访问特殊审批赠送功能验证

##### PD-前置条件：用户无特殊审批赠送相关权限；

##### 步骤一：使用无权限用户登录系统

##### 步骤二：尝试访问特殊审批赠送创建页面

##### 步骤三：尝试调用相关接口

##### ER-预期结果：1：页面访问被拒绝；2：接口调用返回权限不足错误；3：系统记录访问日志；

#### TL-越权操作其他用户特殊审批赠送验证

##### PD-前置条件：存在多个用户的特殊审批赠送数据；

##### 步骤一：使用用户A登录系统

##### 步骤二：尝试操作用户B创建的特殊审批赠送

##### 步骤三：验证权限控制效果

##### ER-预期结果：1：无法查看其他用户数据；2：操作被拒绝；3：系统记录越权尝试日志；

## 兼容性测试

### 多端验证测试

#### TL-PC端H5端特殊审批赠送功能一致性验证

##### PD-前置条件：PC端和H5端均部署最新版本；

##### 步骤一：分别在PC端和H5端登录同一账号

##### 步骤二：在两端分别创建特殊审批赠送

##### 步骤三：验证功能表现一致性

##### 步骤四：验证数据同步情况

##### ER-预期结果：1：两端功能表现一致；2：界面适配正常；3：数据实时同步；4：操作体验流畅；

## 性能测试

### 并发性能测试

#### TL-特殊审批赠送创建并发性能验证

##### PD-前置条件：测试环境性能稳定；准备并发测试数据；

##### 步骤一：设计100个并发用户同时创建特殊审批赠送

##### 步骤二：执行并发测试

##### 步骤三：监控系统响应时间和资源使用情况

##### 步骤四：验证数据一致性

##### ER-预期结果：1：系统响应时间在可接受范围内；2：无数据丢失或重复；3：系统资源使用正常；4：无死锁或异常情况；

## 业务异常测试

### 逆向流程测试

#### TL-已作废特殊审批赠送重新激活验证

##### PD-前置条件：存在已作废状态的特殊审批赠送；

##### 步骤一：登录运营支撑系统，查找已作废的特殊审批赠送

##### 步骤二：尝试对已作废记录进行重新激活操作

##### 步骤三：验证系统处理结果

##### ER-预期结果：1：系统不支持已作废记录重新激活；2：相关操作按钮不可见或不可点击；3：如有操作尝试则提示不支持；

#### TL-已完成特殊审批赠送状态回退验证

##### PD-前置条件：存在已完成状态的特殊审批赠送；

##### 步骤一：登录运营支撑系统，查找已完成的特殊审批赠送

##### 步骤二：尝试将已完成状态回退到待生效状态

##### 步骤三：验证状态流转控制

##### ER-预期结果：1：系统不支持已完成状态回退；2：状态机控制正确；3：操作被拒绝并提示；

### 冲突操作测试

#### TL-多人同时操作同一特殊审批赠送验证

##### PD-前置条件：存在待生效状态的特殊审批赠送；多个用户具有操作权限；

##### 步骤一：用户A和用户B同时登录系统

##### 步骤二：两用户同时对同一条待生效记录进行生效操作

##### 步骤三：验证并发控制机制

##### ER-预期结果：1：只有一个用户操作成功；2：另一用户收到操作冲突提示；3：数据状态保持一致；4：无重复生效情况；

#### TL-特殊审批赠送生效过程中合同状态变更冲突

##### PD-前置条件：存在关联合同的待生效特殊审批赠送；

##### 步骤一：启动特殊审批赠送生效操作

##### 步骤二：在生效过程中同时变更关联合同状态

##### 步骤三：验证冲突处理机制

##### ER-预期结果：1：系统检测到状态冲突；2：生效操作失败或回滚；3：提示合同状态已变更；4：数据一致性得到保证；

### 边界规则测试

#### TL-生效时长边界值366天验证

##### PD-前置条件：具有特殊审批赠送创建权限；

##### 步骤一：创建特殊审批赠送，设置生效时长为366天

##### 步骤二：完成其他信息填写并提交

##### 步骤三：验证边界值处理

##### ER-预期结果：1：366天边界值被接受；2：特殊审批赠送创建成功；3：生效时长计算正确；

#### TL-生效时长超出边界值367天验证

##### PD-前置条件：具有特殊审批赠送创建权限；

##### 步骤一：创建特殊审批赠送，尝试设置生效时长为367天

##### 步骤二：验证输入校验

##### ER-预期结果：1：系统拒绝367天输入；2：提示生效时长限制1-366整数；3：无法保存超出边界的值；

#### TL-生效时长最小边界值1天验证

##### PD-前置条件：具有特殊审批赠送创建权限；

##### 步骤一：创建特殊审批赠送，设置生效时长为1天

##### 步骤二：完成其他信息填写并提交

##### 步骤三：验证最小边界值处理

##### ER-预期结果：1：1天最小边界值被接受；2：特殊审批赠送创建成功；3：生效时长计算正确；

## 系统异常测试

### 依赖服务异常测试

#### TL-合同管理系统服务异常时关联合同校验

##### PD-前置条件：合同管理系统服务不可用；

##### 步骤一：登录运营支撑系统，进入特殊审批赠送创建页面

##### 步骤二：录入客户信息和合同编号

##### 步骤三：尝试进行合同关联操作

##### ER-预期结果：1：系统检测到合同服务异常；2：提示合同服务暂不可用；3：关联操作失败但不影响其他功能；4：记录异常日志；

#### TL-SAAS签署系统异常时赠送告知函创建

##### PD-前置条件：SAAS签署系统服务异常；存在需要创建告知函的审批通过记录；

##### 步骤一：特殊审批赠送审批通过触发告知函创建

##### 步骤二：系统调用SAAS签署服务创建告知函

##### 步骤三：验证异常处理机制

##### ER-预期结果：1：检测到SAAS服务异常；2：告知函创建失败但不影响审批状态；3：记录创建失败日志；4：支持后续重试机制；

### 资源枯竭测试

#### TL-数据库连接池满时特殊审批赠送操作

##### PD-前置条件：数据库连接池接近满载；

##### 步骤一：在高并发场景下创建特殊审批赠送

##### 步骤二：监控数据库连接使用情况

##### 步骤三：验证连接池管理机制

##### ER-预期结果：1：系统合理管理数据库连接；2：连接池满时进行排队等待；3：超时后给出友好提示；4：不出现连接泄露；

#### TL-磁盘空间不足时赠送告知函文件生成

##### PD-前置条件：服务器磁盘空间不足；

##### 步骤一：触发赠送告知函生成操作

##### 步骤二：系统尝试生成PDF文件

##### 步骤三：验证磁盘空间异常处理

##### ER-预期结果：1：检测到磁盘空间不足；2：文件生成失败并记录日志；3：给出明确错误提示；4：不影响其他业务功能；

## 冒烟测试用例

### 核心功能冒烟测试

#### MYTL-特殊审批赠送关联合同基本流程冒烟测试

##### PD-前置条件：系统正常运行；具有测试权限；存在测试数据；

##### 步骤一：登录系统创建特殊审批赠送并关联合同

##### 步骤二：提交审批并通过审批流程

##### 步骤三：验证待生效状态和相关操作

##### 步骤四：执行生效操作验证订单生成

##### ER-预期结果：1：关联合同功能正常；2：审批流程正常；3：状态流转正确；4：订单生成成功；

#### MYTL-赠送告知函创建基本流程冒烟测试

##### PD-前置条件：系统正常运行；SAAS服务可用；

##### 步骤一：创建营销赠送并勾选同步创建告知函

##### 步骤二：关联合同并完成审批

##### 步骤三：验证告知函自动生成

##### ER-预期结果：1：告知函创建功能正常；2：文件内容正确；3：下载功能可用；

#### MYTL-有效期优化功能冒烟测试

##### PD-前置条件：系统正常运行；

##### 步骤一：创建特殊审批赠送选择商品

##### 步骤二：配置生效日期和生效时长

##### 步骤三：验证有效期计算正确性

##### ER-预期结果：1：有效期配置界面正常；2：日期时长计算正确；3：保存功能正常；

## 线上验证用例

### 核心业务流程线上验证

#### PATL-生产环境特殊审批赠送完整流程验证

##### PD-前置条件：生产环境部署完成；具有生产环境测试权限；

##### 步骤一：在生产环境创建真实的特殊审批赠送

##### 步骤二：关联真实合同并完成审批流程

##### 步骤三：验证生效后的订单和计费情况

##### 步骤四：验证用户端展示和功能可用性

##### ER-预期结果：1：生产环境功能完全可用；2：数据流转正确；3：用户体验良好；4：计费准确无误；

#### PATL-生产环境赠送告知函生成和签署验证

##### PD-前置条件：生产环境SAAS服务正常；

##### 步骤一：创建需要告知函的特殊审批赠送

##### 步骤二：完成审批触发告知函生成

##### 步骤三：验证告知函签署和下载功能

##### ER-预期结果：1：告知函生成正常；2：签署流程完整；3：文件下载可用；4：内容准确完整；

#### PATL-生产环境多用户并发操作验证

##### PD-前置条件：生产环境稳定运行；多个真实用户账号；

##### 步骤一：多用户同时进行特殊审批赠送操作

##### 步骤二：验证系统并发处理能力

##### 步骤三：检查数据一致性和性能表现

##### ER-预期结果：1：并发处理正常；2：响应时间可接受；3：数据完全一致；4：无异常错误；
